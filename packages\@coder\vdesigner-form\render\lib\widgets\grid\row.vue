<script setup lang="ts">
import type { WidgetPropsType } from '@coder/vdesigner-core';

import { onMounted, reactive, ref } from 'vue';
import { useDraggable } from 'vue-draggable-plus';

import { AddIcon, MakeAntDesignIcon } from '@vben/icons';

import { Toolbar, ToolbarButton } from '@coder/toolbar';
import { useMitter, useWidget, useWidgetRegistry } from '@coder/vdesigner-core';
import { Row as ARow, Col } from 'ant-design-vue';

import CoderCol from './col.vue';
import { RowOptions } from './RowOptions';

defineOptions({ name: 'CoderVDesignRow' });

const props = defineProps<WidgetPropsType>();
const widgetRegistry = useWidgetRegistry();
const { childWidgets, isDesign } = useWidget(props.widget, props.renderId);

const options = reactive(props.widget.options as RowOptions);

const rowRef = ref();
const mitt = useMitter(props.renderId);
onMounted(() => {
  mitt.emitAddWidget(rowRef.value.$el, props.widget, props.parentWidget);
});
const onSelectWidget = (e: Event) => {
  e.stopPropagation();
  e.preventDefault();
  mitt.emitSelect(props.widget, props.parentWidget);
};

useDraggable(rowRef, childWidgets, {
  animation: 150,
  handle: '.col-handle',
  group: 'row',
});
const onAddCol = () => {
  if (CoderCol.name)
    childWidgets.value.push(widgetRegistry.createWidget(CoderCol.name));
};
/**
 * 是否显示item中的widget，因为某些widge他会完全挡住了菜单。
 */
const showWidget = ref(true);
</script>
<template>
  <ARow>
    <Col :span="24" v-if="isDesign">
      <Toolbar v-if="isDesign">
        <ToolbarButton @click="onAddCol"> <AddIcon /> </ToolbarButton>

        <ToolbarButton
          v-if="props.parentWidget"
          @click="(e: any) => onSelectWidget(e)"
        >
          <component :is="MakeAntDesignIcon('lucide:settings')" />
        </ToolbarButton>

        <ToolbarButton @click="() => (showWidget = !showWidget)">
          <component v-if="showWidget" :is="MakeAntDesignIcon('lucide:eye')" />
          <component v-else :is="MakeAntDesignIcon('lucide:eye-off')" />
        </ToolbarButton>
      </Toolbar>
    </Col>
  </ARow>
  <ARow
    ref="rowRef"
    :align="options.align"
    :class="{ 'drop-area': isDesign }"
    :gutter="options.gutter"
    :wrap="options.wrap"
    v-widget-menu="{
      widgetProps: props,
    }"
  >
    <CoderCol
      v-for="col in childWidgets.filter((_) => _)"
      :key="col.id"
      :is-design="false"
      :render-id="props.renderId"
      :widget="col"
      :show-widget="showWidget"
      :parent-widget="props.widget"
    />
  </ARow>
</template>

<style lang="less" scoped>
.row-designer {
  flex-grow: 1;
}
</style>
