import { isFunction } from 'lodash-es';
/**
 * vform插件。
 */
export type IPlugin = {
  /**
   * 在plugin中的类名字，如 Router，User
   * 在创建代码提示的时候会生成 name:className的模式，例如name是axios，className是AxiosStatic|AxiosInstance
   * plugins={
   *   axios:AxiosStatic|AxiosInstance
   * }
   */
  className: string;
  /**
   * 获取一个实例。在自定义代码中实现。
   */
  instance: object | { (): any };
  /**
   * 类的定义。
   */
  intellisense?: string;
  /**
   * pluginName,需要符合js 变量定义
   */
  name: string;
};

const pluginNames = {} as Record<string, IPlugin>;

/**
 * 注册插件
 * @param plugin 插件
 */
const register = (plugin: IPlugin) => {
  pluginNames[plugin.name] = plugin;
};

/**
 * 移除插件
 * @param name 插件名称
 */
export const unregister = (name: string) => {
  // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
  delete pluginNames[name];
};

let pluginsInstances: Record<string, any>;

export const usePlugins = () => {
  return {
    getInstance: (name: string) => {
      const t = pluginNames[name];

      if (!t) throw new Error(`${name}不存在。`);

      const instan = isFunction(t?.instance) ? t.instance() : t?.instance;
      return Promise.resolve(instan);
    },

    /**
     * 插件实例
     */
    getInstances: () => {
      if (pluginsInstances) return pluginsInstances;

      pluginsInstances = {} as Record<string, any>;
      for (const property in pluginNames) {
        const plugin = pluginNames[property];
        if (!plugin) continue;
        pluginsInstances[property] = isFunction(plugin?.instance)
          ? plugin.instance()
          : plugin?.instance;
      }
      return pluginsInstances;
    },
    /**
     *
     * @param name 名字
     * @returns
     */
    getPlugin: (name: string): IPlugin => {
      const t = pluginNames[name];
      if (!t) throw new Error(`${name}不存在。`);
      return t;
    },

    /**
     * plugin提示
     * @returns string 提示
     */
    getPluginIntellisense: (setIntellsense: {
      (key: string, code: string): void;
    }) => {
      const { plugins } = usePlugins();
      const list: string[] = [];

      for (const key in plugins) {
        const plugin = plugins[key];
        if (!plugin) continue;
        if (plugin.intellisense) {
          setIntellsense(`plugin_${plugin.name}`, plugin.intellisense);
        }
        list.push(`${plugin.name}:${plugins[key]?.className}`);
      }

      const property = list.join(';\n');
      const result = `
      declare type VDesignPluginClass = { 
        ${property}
      }
      declare let plugins:VDesignPluginClass;
      `;

      return result;
    },
    plugins: pluginNames,
    /**
     * 注册plugin
     */
    register,
    /**
     * 反注册reisgr
     */
    unregister,
  };
};
