<script setup lang="ts">
import type { WidgetPropsType } from '@coder/vdesigner-core';

/**
 * 容器内容组件，用于解决设计模式和非设计模式复杂读问题。
 */
import { computed } from 'vue';

import {
  useRenderStore,
  useWidget,
  useWidgetRegistry,
} from '@coder/vdesigner-core';

const props = defineProps<WidgetPropsType>();
const { childWidgets } = useWidget(props.widget, props.renderId);
const { getComponent } = useWidgetRegistry();

const store = useRenderStore(props.renderId);
const implement = computed(() => store.implement);
</script>

<template>
  <template
    v-for="childWidget in childWidgets.filter((_) => _)"
    :key="childWidget.key"
  >
    <component
      :is="getComponent(childWidget.type, implement)"
      :parent-widget="props.widget"
      :render-id="props.renderId"
      :widget="childWidget"
    />
  </template>
</template>
