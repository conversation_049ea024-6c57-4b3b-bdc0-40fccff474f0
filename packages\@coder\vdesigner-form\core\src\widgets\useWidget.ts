import type { Ref } from 'vue';

import type { Widget } from './Widget';

import {
  computed,
  onActivated,
  onDeactivated,
  onMounted,
  onUnmounted,
} from 'vue';

import { computedAsync } from '@vueuse/core';
import { storeToRefs } from 'pinia';

import { useDataSource } from '../dataSource';
import { useRenderDataSourceStore, useRenderStore } from '../render';
import { useCodeInvoker } from '../useCodeInvoke';
import { useWidgetRegistry } from '../useWidgetRegistry';
import { getValue as getVal, getValueFrom, setValue as setVal } from '../utils';
/**
 * 辅助创建widget, 用vue
 * @param widgetPorps
 */
export const useWidget = (widget: Widget, renderId: string): typeof result => {
  if (!renderId) throw new Error('renderId is undefined.');
  const renderStore = useRenderStore(renderId);
  const dataSourceStore = useRenderDataSourceStore(renderId);
  const { getData } = storeToRefs(dataSourceStore) as any;
  const { invokeWidgetCode } = useCodeInvoker(renderStore);

  const { getDefine } = useWidgetRegistry();
  const definition = getDefine(widget.type);

  const value = computed({
    get: () => getVal(widget, renderStore),
    set: (v) => setVal(widget, v, renderStore),
  });
  const getValue = () => {
    return getVal(widget, renderStore);
  };
  const setValue = (value: any) => {
    return setVal(widget, value, renderStore);
  };

  const getFromDs = (optionName: string) => {
    const dsName = widget.dataSourceRef[optionName];
    return getData.value(dsName?.dataSourceId ?? '');
  };

  const getWidgetOption = <T>(optionName: any): Ref<T> => {
    // form dataSource
    const dsRef = widget.dataSourceRef[optionName];
    if (dsRef) {
      return computed(() => {
        const data = getData.value(dsRef?.dataSourceId ?? '');
        return useDataSource(renderId).translateByDsRef(dsRef, data);
      });
    }

    const val = widget.options[optionName];
    if (typeof val === 'string') {
      if (val.startsWith('fd:')) {
        // 从renderStore.formData 获取数据
        return computed(() => {
          const path = val.slice(3);
          return getValueFrom(path, renderStore.formData);
        });
      } else if (val.startsWith('code:')) {
        // 通过代码获取数据
        const code = val.slice(5);
        // 这里不允许返回Promise都西昂，或者
        return computedAsync(async () => {
          const codeInvokeResult = await invokeWidgetCode(widget, code);
          return codeInvokeResult;
        });
      } else if (val.startsWith('cache:')) {
        // 从dataSourceStore.data 获取数据
        return computed(() => {
          const path = val.slice(6);
          return getValueFrom(path, dataSourceStore.data);
        });
      }
    }
    return computed(() => widget.options[optionName]);
  };

  const result = {
    callJsCode: (code: string, addtionVal: any = undefined) => {
      return invokeWidgetCode(widget, code, addtionVal);
    },

    childWidgets: computed({
      get: () => {
        if (!widget.widgets) {
          widget.widgets = [];
        }
        return widget.widgets;
      },
      set: (v) => {
        if (v) {
          widget.widgets.splice(0);
          widget.widgets.push(...v);
        }
      },
    }),
    formData: computed(() => renderStore.formData),

    getValue,
    getFromDs,

    /**
     * 获取带有可能出现前序：如 fd: cache: code:的计算属性。
     */

    getWidgetOption,

    implement: computed(() => renderStore.implement),

    isDesign: computed(() => renderStore.isDesign ?? false),

    rootWidget: computed(() => renderStore.renderConfig.rootWidget),
    setValue,
    updateByDataSource: (
      optionName: string,
      additionData?: Record<string, any>,
    ): void => {
      useDataSource(renderId).invokeByRef(optionName, widget, additionData);
    },

    value,

    widget: computed(() => widget),
  };

  onMounted(() => {
    if (widget.options.mountedEvent && !renderStore.isDesign) {
      invokeWidgetCode(widget, widget.options.mountedEvent);
    }
    if (definition.onInit) {
      definition.onInit(result);
    }
  });
  onUnmounted(() => {
    if (!widget.options.unmountedEvent || renderStore.isDesign) return;
    invokeWidgetCode(widget, widget.options.unmountedEvent);
  });

  onActivated(() => {
    if (!widget.options.activatedEvent || renderStore.isDesign) return;
    invokeWidgetCode(widget, widget.options.activatedEvent);
  });

  onDeactivated(() => {
    if (!widget.options.deactivatedEvent || renderStore.isDesign) return;
    invokeWidgetCode(widget, widget.options.deactivatedEvent);
  });

  return result;
};
