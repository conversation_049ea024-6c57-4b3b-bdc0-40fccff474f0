<script setup lang="ts">
import type { WidgetPropsType } from '@coder/vdesigner-core';

import { computed, onMounted, ref, toRaw } from 'vue';

import { useMitter, useWidget } from '@coder/vdesigner-core';
import { FormOptions, InputOptions } from '@coder/vdesigner-form-render';
import { Input as AInput } from 'ant-design-vue';

import FormItem from '../formItem/formItem.vue';

const props = defineProps<WidgetPropsType & {}>();

const { callJsCode, isDesign, rootWidget, value } = useWidget(
  props.widget,
  props.renderId,
);
const formOptions = computed(() => {
  return rootWidget.value?.options as FormOptions;
});

const options = props.widget.options as InputOptions;

const size = computed(
  () => options.size ?? formOptions.value.size ?? undefined,
);

const onchange = () => {
  if (!options.changeEvent || isDesign.value) return;
  callJsCode(options.changeEvent);
};

const hiddenRef = ref();

onMounted(() => {
  const renderMitter = useMitter(props.renderId);

  if (hiddenRef.value) {
    renderMitter.emitAddWidget(
      hiddenRef.value,
      toRaw(props.widget),
      props.parentWidget,
    );
  }
});
</script>

<template>
  <FormItem
    :parent-widget="props.parentWidget"
    :render-id="props.renderId"
    :widget="props.widget"
    :rules="props.widget.rules"
  >
    <input
      v-if="options.hidden === true"
      v-widget-menu="{ widgetProps: props }"
      ref="hiddenRef"
      v-model="value"
      :type="isDesign ? 'text' : 'hidden'"
      placeholder="hidden-text"
    />
    <AInput
      v-else
      v-model:value="value"
      :allow-clear="options.allowClear"
      :disabled="options.disabled === true"
      :maxlength="options.maxlength"
      :placeholder="options.placeholder"
      :readonly="options.readonly === true"
      :show-count="options.showCount"
      :size="size"
      :type="options.type"
      @change="onchange"
    />
  </FormItem>
</template>
