import type { DataSource } from '../dataSource';
import type { Widget } from '../widgets/index';
import type { RenderConfig, RenderPropsType } from './RenderTypes';

import { useWidgetRegistry } from '../useWidgetRegistry';
import { v1ToV2 } from './v1ToV2';

const createRootWidget = (): Widget => {
  // const nameId = `rootWidget${generateId()}`;
  const registry = useWidgetRegistry();
  const root = registry.createWidget('CoderVDesignPage');

  return root;
};

const makePlanObject = (result: Record<string, Widget>, widgets: Widget[]) => {
  widgets.forEach((childWidget) => {
    if (childWidget === undefined) return;
    result[childWidget.id] = childWidget;
    if (childWidget.widgets && childWidget.widgets.length > 0) {
      makePlanObject(result, childWidget.widgets);
    }
  });
};

/**
 * 生成 key为 widget.name ， value为Widget的对象。
 * @param rootWidget widget
 * @return widget 对象集合
 */
export const toPlantWidgets = (rootWidget: Widget): Record<string, Widget> => {
  const result = {} as Record<string, Widget>;

  result[rootWidget.id] = rootWidget;

  if (rootWidget.widgets && rootWidget.widgets.length > 0) {
    makePlanObject(result, rootWidget.widgets);
  }

  return result;
};

export const getRenderId = (renderInfo: { rootWidget?: Widget }) => {
  if (!renderInfo.rootWidget) {
    renderInfo.rootWidget = createRootWidget();
  }
  return renderInfo.rootWidget.id;
};

/**
 *
 * @param props
 * @returns
 */
export const initRootWidget = (props: RenderPropsType): RenderConfig => {
  if (!props.renderConfig) {
    return {
      dataSource: new Array<DataSource>(),
      rootWidget: createRootWidget(),
    } as RenderConfig;
  }

  if ((props.renderConfig as any).widgetList !== undefined) {
    props.renderConfig.rootWidget = v1ToV2(
      (props.renderConfig as any).widgetList as any,
      props.cfg ?? {},
    );
    delete (props.renderConfig as any).widgetList;
    delete (props.renderConfig as any).formConfig;
  }

  const rootWidget = props.renderConfig.rootWidget ?? createRootWidget();

  if (!props.renderConfig.rootWidget) {
    props.renderConfig.rootWidget = rootWidget;
  }
  if (!props.renderConfig.dataSource) {
    props.renderConfig.dataSource = new Array<DataSource>();
  }
  if (!rootWidget?.options) {
    const t = {};
    Object.assign(t, createRootWidget(), rootWidget);
    Object.assign(rootWidget, t);
  }
  return {
    dataSource: props.renderConfig.dataSource,
    rootWidget: props.renderConfig.rootWidget,
  };
};
