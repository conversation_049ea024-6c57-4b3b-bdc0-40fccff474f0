<script setup lang="ts">
import type { Widget, WidgetPropsType } from '@coder/vdesigner-core';
import type { LayoutItem } from 'grid-layout-plus';

import { computed, onMounted, ref } from 'vue';

import { DeleteIcon as CloseIcon, MakeAntDesignIcon } from '@vben/icons';

import { FullscreenOutlined } from '@ant-design/icons-vue';
import {
  useMitter,
  useRenderStore,
  useWidget,
  useWidgetRegistry,
} from '@coder/vdesigner-core';

import { useDragItem } from '../_dragGridUtility';
import { DragGridItemName } from './_options';

defineOptions({ name: DragGridItemName });
const props = defineProps<
  WidgetPropsType & {
    layoutItem: LayoutItem;
    marginY: number;
    rowHeight: number;
    showWidget: boolean;
  }
>();

const emits = defineEmits<{
  (e: 'delete', widget: Widget): void;
}>();
const store = useRenderStore(props.renderId);
const { getComponent } = useWidgetRegistry();
const imple = computed(() => store.implement);
const inDesign = computed(() => store.isDesign);
const { childWidgets } = useWidget(props.widget, props.renderId);

const colRef = ref();
const mitter = useMitter(props.renderId);
const { contentRef } = useDragItem(props, emits);

mitter.emitAddContainer(colRef, childWidgets, props.parentWidget);
const widgetOptions = computed(() => props.widget.options);
const onSelect = (e: Event) => {
  e.stopPropagation();
  e.preventDefault();
  isSelected.value = true;
  mitter.emitSelect(props.widget);
};
const onRemove = () => {
  emits('delete', props.widget);
  // 触发删除事件
  mitter.emitDeleteWidget(props.widget);
};
const style = computed(() => {
  let bg = widgetOptions.value.backgroundColor;

  if (!props.showWidget) {
    bg = '#dedede';
  }
  return {
    backgroundColor: `${bg} !important`,
    minHeight: '100px',
    textAlign: 'center',
  };
});

const isSelected = ref(false);
const inDev = computed(() => store.isDev);
onMounted(() => {
  mitter.onUnSelect(({ widget }) => {
    if (widget.id === props.widget.id) {
      isSelected.value = false;
    }
  });
});

const testItemInfo = computed(() => {
  return `h: ${props.layoutItem.h},w: ${props.layoutItem.w},x: ${props.layoutItem.x},y: ${props.layoutItem.y}`;
});
const onSelectWidget = (childWidget: Widget) => {
  isSelected.value = true;
  mitter.emitSelect(childWidget, props.widget);
};

const showWidgetInfo = (childWidget: Widget) => {
  return `${childWidget.id}/${childWidget.type.slice(12)}`;
};
</script>

<template>
  <div
    @click="onSelect"
    :class="{ selected: isSelected }"
    class="grid-item"
    ref="contentRef"
    v-if="inDesign"
  >
    <div class="dev-header-menus" v-if="inDev">
      <a style="display: inline">
        {{ testItemInfo }}
      </a>
    </div>
    <div class="vue-header-menus" v-if="inDesign">
      <a class="draggable-handle">
        <FullscreenOutlined />
      </a>

      <a class="remove-btn" @click="onRemove">
        <component :is="CloseIcon" />
      </a>

      <a class="select-handle" title="调出页面项菜单那" style="z-index: 105">
        <component
          :is="MakeAntDesignIcon('lucide:locate-fixed')"
          @click="onSelect"
        />
      </a>
    </div>

    <div class="no-drag" ref="colRef" :style="style as any">
      <template
        v-for="childWidget in childWidgets.filter((_) => _)"
        :key="childWidget.id"
      >
        <component
          v-show="showWidget"
          :is="getComponent(childWidget.type, imple)"
          :parent-widget="props.parentWidget"
          :render-id="props.renderId"
          :widget="childWidget"
        />
        <ul v-show="!showWidget">
          <li>
            <a @click.stop="() => onSelectWidget(childWidget)">
              {{ showWidgetInfo(childWidget) }}
            </a>
          </li>
        </ul>
      </template>
    </div>
  </div>
  <div v-else ref="contentRef">
    <template
      v-for="childWidget in childWidgets.filter((_) => _)"
      :key="childWidget.key"
    >
      <component
        :is="getComponent(childWidget.type, imple)"
        :parent-widget="props.parentWidget"
        :render-id="props.renderId"
        :widget="childWidget"
      />
    </template>
  </div>
</template>
