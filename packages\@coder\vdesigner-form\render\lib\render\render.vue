<script setup lang="tsx">
import type { RenderPropsType, Widget } from '@coder/vdesigner-core';

import { computed, nextTick, onMounted, ref } from 'vue';

import { DatabaseOutlined } from '@ant-design/icons-vue';
import { CodeEditor } from '@coder/code-editor';
import { Toolbar, ToolbarButton } from '@coder/toolbar';
import {
  useDataSource,
  useMitter,
  usePlugins,
  useRender,
  useRenderStore,
  useWidgetRegistry,
} from '@coder/vdesigner-core';
import { useEventListener } from '@vueuse/core';
import { message, Modal } from 'ant-design-vue';

const props = defineProps<RenderPropsType>();
const emits = defineEmits<{
  (e: 'addContainer', t: { element: any; widgets: any }): void;
  (e: 'addWidget', t: { element: any; widget: Widget }): void;
  (e: 'deleteWidget', t: { widget: Widget }): void;
  (e: 'load', t: { renderId: string }): void;
}>();

const { initDataSource, renderId } = useRender(props, (renderId) => {
  emits('load', { renderId });
});

const renderStore = useRenderStore(renderId);

const { onFormDataChange } = useDataSource(renderId);
const renderMitter = useMitter(renderId);
const { getComponent } = useWidgetRegistry();
/**
 * RootWidget 是通过设置获取的，因此要通过getComponent获取
 */
const RootWidget = getComponent(
  renderStore.renderConfig.rootWidget!.type,
  props.implement,
);
const renderModel = computed(() => renderStore.renderConfig);
renderMitter.onAddContainer((data) => {
  emits('addContainer', data);
});
renderMitter.onAddWidget((data) => {
  emits('addWidget', data);
});
renderMitter.onDeleteWidget((data) => {
  emits('deleteWidget', data);
});
const refresh = ref(true);
onMounted(() => {
  initDataSource();
});

defineExpose({
  getFormData: () => {
    return renderStore.formData;
  },
  /**
   * 获取functions
   */
  getInstance: (): Record<string, any> => {
    return renderStore.functions;
  },
  setFormData: (v: any) => {
    refresh.value = false;
    renderStore.formData = v;
    nextTick(() => (refresh.value = true));
  },
  setRenderConfig: (config: any) => {
    refresh.value = false;
    renderStore.importFrom(config);
    nextTick(() => (refresh.value = true));
  },
});
useEventListener('keydown', (e) => {
  if (e.ctrlKey && e.altKey && e.key === 'd') {
    e.preventDefault();
    // console.log('Ctrl+Alt+D pressed');
    // 在这里添加你的逻辑
    renderStore.setDev(!renderStore.$state.isDev);
    message.info(
      `当前模式: ${renderStore.$state.isDev ? '开发模式' : '生产模式'}`,
    );
  }
});

// is dev 开发模式
const onShowJsonData = () => {
  const code = JSON.stringify(renderStore.formData, null, 2);
  Modal.info({
    content: () => {
      return <CodeEditor code={code} type="json" />;
    },
    title: 'FormData',
    width: 800,
  });
};

onMounted(() => {
  if (!renderStore.isDesign) onFormDataChange();
});

// 因为插件中，有部分instance需要在setup执行，如UseRoute，因此这里进行初始化。
const { getInstances } = usePlugins();
getInstances();
</script>

<template>
  <!--
  获取rootWidget.
  -->
  <Toolbar v-if="renderStore.isDev">
    <ToolbarButton @click="onShowJsonData"><DatabaseOutlined /></ToolbarButton>
  </Toolbar>

  <RootWidget
    v-if="refresh"
    :render-id="renderId"
    :widget="renderModel.rootWidget"
  />
</template>
