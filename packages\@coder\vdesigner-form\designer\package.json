{"name": "@coder/vdesigner-form-designer", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": " vite build", "dev": "vite", "preview": "vite preview"}, "main": "lib/index.ts", "typings": "lib/index.ts", "publishConfig": {"main": "dist/index.mjs", "exports": {".": {"import": "./dist/index.mjs"}}, "typings": "dist/index.d.ts"}, "dependencies": {"@ant-design/icons-vue": "catalog:coder", "@coder/code-editor": "workspace:*", "@coder/http-request-setting": "workspace:*", "@coder/monaco-editor-builder": "workspace:^", "@coder/object-editor": "workspace:*", "@coder/rich-editor": "workspace:", "@coder/string-format": "workspace:*", "@coder/vdesigner-core": "workspace:^", "@coder/vdesigner-form-antdv": "workspace:*", "@coder/vdesigner-form-render": "workspace:^", "@coder/vdesigner-plugins-axios": "workspace:^", "@coder/vdesigner-plugins-router": "workspace:^", "@vben-core/icons": "workspace:*", "@vben-core/shadcn-ui": "workspace:^", "@vben/icons": "workspace:^", "@vben/request": "workspace:^", "@vben/styles": "workspace:*", "ant-design-vue": "catalog:", "axios": "catalog:", "dom-align": "catalog:coder", "lodash-es": "catalog:coder", "mitt": "catalog:", "monaco-editor": "catalog:", "openapi-typescript": "catalog:", "pinia": "catalog:", "splitpanes": "catalog:coder", "vue": "catalog:", "vue-draggable-plus": "catalog:coder", "vue3-colorpicker": "catalog:coder"}, "devDependencies": {"@coder/system-api": "workspace:*", "@coder/vdesigner-form-vant": "workspace:^", "@coder/vdesigner-widget-http-file": "workspace:^", "@types/lodash-es": "catalog:coder", "@types/splitpanes": "catalog:coder", "@vben/tsconfig": "workspace:*", "@vben/vite-config": "workspace:*", "@vitejs/plugin-vue": "catalog:", "mockjs": "catalog:", "vite-plugin-mock": "catalog:coder"}}